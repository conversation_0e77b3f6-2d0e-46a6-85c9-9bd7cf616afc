[{"transactionId": "TXN-MGL100000-M2", "invoiceNumber": "MGL100000-M2", "projectId": 301, "freelancerId": 31, "commissionerId": 32, "amount": 1748, "status": "paid", "integration": "mock", "timestamp": "2025-08-07T19:51:48.416Z", "paymentGatewayResponse": null, "executionResult": {"success": true, "mockPayment": true}}, {"transactionId": "TXN-MGL000301-M3", "invoiceNumber": "MGL000301-M3", "projectId": 301, "freelancerId": 31, "commissionerId": 32, "amount": 1500, "status": "paid", "integration": "mock", "timestamp": "2025-08-07T19:53:06.412Z", "paymentGatewayResponse": null, "executionResult": {"success": true, "mockPayment": true}}]