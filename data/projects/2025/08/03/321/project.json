{"projectId": 321, "title": "Park Maintenance Mobile App", "description": "Develop a mobile application for park maintenance staff to report issues, track maintenance tasks, and coordinate with central operations. The app should work offline and sync when connected.", "organizationId": 1, "typeTags": ["React Native", "Mobile", "iOS", "Android", "GPS"], "manager": {"name": "<PERSON><PERSON>", "title": "Product Manager", "avatar": "/avatars/neilsan.png", "email": "<EMAIL>"}, "commissionerId": 32, "freelancerId": 11, "status": "ongoing", "dueDate": "2025-09-28", "totalTasks": 1, "createdAt": "2025-08-03T15:17:21.233Z", "invoicingMethod": "completion", "budget": {"lower": 1000, "upper": 5000, "currency": "USD"}}