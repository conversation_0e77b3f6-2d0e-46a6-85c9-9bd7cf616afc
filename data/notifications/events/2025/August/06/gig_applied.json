[{"id": "gig_applied_13_1_1754528710228", "timestamp": "2025-08-07T01:05:10.228Z", "type": "gig_applied", "notificationType": 60, "actorId": 1, "targetId": 35, "entityType": 3, "entityId": "13", "metadata": {"gigTitle": "Event Venue 3D Visualization", "applicationMessage": "Application submitted", "freelancerName": "<PERSON><PERSON>"}, "context": {"gigId": 13, "applicationId": 20}}, {"id": "gig_applied_13_1_1754526200136", "timestamp": "2025-08-07T00:23:20.136Z", "type": "gig_applied", "notificationType": 60, "actorId": 1, "targetId": 35, "entityType": 3, "entityId": "13", "metadata": {"gigTitle": "Event Venue 3D Visualization", "applicationMessage": "Application submitted", "freelancerName": "<PERSON><PERSON>"}, "context": {"gigId": 13, "applicationId": 19}}, {"id": "gig_applied_12_22_1754499900000", "timestamp": "2025-08-06T17:05:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 22, "targetId": 35, "entityType": 3, "entityId": "12", "metadata": {"gigTitle": "Corporate Event Photography Package", "applicationMessage": "Application submitted", "freelancerName": "<PERSON><PERSON>"}, "context": {"gigId": 12, "applicationId": 104}}, {"id": "gig_applied_13_14_1754499800000", "timestamp": "2025-08-06T17:03:20.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 14, "targetId": 35, "entityType": 3, "entityId": "13", "metadata": {"gigTitle": "Event Venue 3D Visualization", "applicationMessage": "Application submitted", "freelancerName": "<PERSON>"}, "context": {"gigId": 13, "applicationId": 103}}, {"id": "gig_applied_12_8_1754499700000", "timestamp": "2025-08-06T17:01:40.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 8, "targetId": 35, "entityType": 3, "entityId": "12", "metadata": {"gigTitle": "Corporate Event Photography Package", "applicationMessage": "Application submitted", "freelancerName": "<PERSON><PERSON>"}, "context": {"gigId": 12, "applicationId": 102}}, {"id": "gig_applied_12_5_1754499600000", "timestamp": "2025-08-06T17:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 5, "targetId": 35, "entityType": 3, "entityId": "12", "metadata": {"gigTitle": "Corporate Event Photography Package", "applicationMessage": "Application submitted", "freelancerName": "<PERSON><PERSON>"}, "context": {"gigId": 12, "applicationId": 101}}]