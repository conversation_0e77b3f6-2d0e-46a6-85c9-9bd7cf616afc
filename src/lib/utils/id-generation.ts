/**
 * Robust ID Generation Utilities
 * 
 * Provides collision-resistant ID generation for various entities
 * in the system while maintaining backward compatibility with numeric IDs.
 */

import { randomBytes } from 'crypto';

/**
 * Generate a collision-resistant numeric ID
 * Uses timestamp + crypto random for uniqueness
 */
export function generateNumericId(prefix?: string): number {
  const timestamp = Date.now();
  
  // Use crypto.randomBytes for better randomness
  const randomBuffer = randomBytes(4);
  const randomPart = randomBuffer.readUInt32BE(0) % 100000; // 5 digits max
  
  // Combine timestamp with crypto random
  const id = parseInt(`${timestamp}${randomPart.toString().padStart(5, '0')}`);
  
  // Ensure we don't exceed JavaScript's safe integer limit
  if (id > Number.MAX_SAFE_INTEGER) {
    // Fallback to shorter format
    return Date.now() + (randomPart % 999999);
  }
  
  return id;
}

/**
 * Generate a project ID with collision detection
 */
export function generateProjectId(): number {
  return generateNumericId('project');
}

/**
 * Generate a task ID with collision detection
 */
export function generateTaskId(): number {
  return generateNumericId('task');
}

/**
 * Generate an invoice ID with collision detection
 */
export function generateInvoiceId(): number {
  return generateNumericId('invoice');
}

/**
 * Generate a UUID-style string ID for entities that can use strings
 */
export function generateStringId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = randomBytes(8).toString('hex');
  return `${timestamp}-${randomPart}`;
}

/**
 * Generate a short alphanumeric ID (like invoice numbers)
 */
export function generateShortId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  const randomBuffer = randomBytes(length);
  
  for (let i = 0; i < length; i++) {
    result += chars[randomBuffer[i] % chars.length];
  }
  
  return result;
}

/**
 * Generate a user-prefixed invoice number
 */
export function generateInvoiceNumber(userInitials: string): string {
  const shortId = generateShortId(5);
  return `${userInitials}-${shortId}`;
}

/**
 * Validate that an ID is within safe integer bounds
 */
export function validateNumericId(id: number): boolean {
  return Number.isInteger(id) && id > 0 && id <= Number.MAX_SAFE_INTEGER;
}

/**
 * ID collision detection utility
 * Checks if an ID already exists in a given set
 */
export function checkIdCollision(id: number | string, existingIds: Set<number | string>): boolean {
  return existingIds.has(id);
}

/**
 * Generate a unique ID with collision detection
 * Retries up to maxAttempts times if collisions occur
 */
export function generateUniqueId(
  generator: () => number | string,
  existingIds: Set<number | string>,
  maxAttempts: number = 10
): number | string {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const id = generator();
    if (!checkIdCollision(id, existingIds)) {
      return id;
    }
  }
  
  throw new Error(`Failed to generate unique ID after ${maxAttempts} attempts`);
}

/**
 * Enhanced project ID generation with collision detection
 */
export function generateUniqueProjectId(existingProjectIds: Set<number>): number {
  return generateUniqueId(
    generateProjectId,
    existingProjectIds,
    10
  ) as number;
}

/**
 * Enhanced task ID generation with collision detection
 */
export function generateUniqueTaskId(existingTaskIds: Set<number>): number {
  return generateUniqueId(
    generateTaskId,
    existingTaskIds,
    10
  ) as number;
}

/**
 * Performance monitoring for ID generation
 */
export interface IdGenerationMetrics {
  totalGenerated: number;
  collisions: number;
  averageAttempts: number;
  lastGenerated: string;
}

class IdGenerationMonitor {
  private metrics: IdGenerationMetrics = {
    totalGenerated: 0,
    collisions: 0,
    averageAttempts: 0,
    lastGenerated: new Date().toISOString()
  };

  recordGeneration(attempts: number): void {
    this.metrics.totalGenerated++;
    this.metrics.collisions += attempts - 1;
    this.metrics.averageAttempts = 
      (this.metrics.averageAttempts * (this.metrics.totalGenerated - 1) + attempts) / 
      this.metrics.totalGenerated;
    this.metrics.lastGenerated = new Date().toISOString();
  }

  getMetrics(): IdGenerationMetrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      totalGenerated: 0,
      collisions: 0,
      averageAttempts: 0,
      lastGenerated: new Date().toISOString()
    };
  }
}

export const idGenerationMonitor = new IdGenerationMonitor();
